<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Translate</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-in-out'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .typing-indicator::after {
            content: '|';
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Mobile optimizations */
        @media (max-width: 640px) {
            .record-button-mobile {
                width: 120px !important;
                height: 120px !important;
            }
            .record-button-mobile svg {
                width: 48px !important;
                height: 48px !important;
            }
        }

        /* Prevent zoom on input focus for iOS */
        @media screen and (-webkit-min-device-pixel-ratio: 0) {
            select, input, textarea {
                font-size: 16px !important;
            }
        }

        /* Touch-friendly buttons */
        button {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen font-sans">
    <!-- Auth Gate -->
    <div id="auth-gate" class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-gray-800 rounded-lg shadow-2xl p-8 w-full max-w-md animate-fade-in">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-blue-400 mb-2">Conversation Translator</h1>
                <p class="text-gray-400">Enter access key to continue</p>
            </div>
            <form id="auth-form" class="space-y-6">
                <div>
                    <input 
                        type="password" 
                        id="access-key" 
                        placeholder="Access Key"
                        class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300"
                        required
                    >
                </div>
                <button 
                    type="submit"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-300 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                >
                    Access
                </button>
                <div id="auth-error" class="text-red-400 text-sm text-center hidden"></div>
            </form>
        </div>
    </div>

    <!-- Translation UI -->
    <div id="translation-ui" class="hidden min-h-screen p-4">
        <div class="max-w-6xl mx-auto animate-slide-up">
            <!-- Header -->
            <header class="text-center mb-6">
                <h1 class="text-3xl md:text-4xl font-bold text-blue-400 mb-2">Conversation Translator</h1>
                <p class="text-gray-400">Real-time conversation between two languages</p>
            </header>

            <!-- Conversation Interface -->
            <div class="max-w-6xl mx-auto">
                <!-- Language Settings -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    <div class="bg-blue-900/30 rounded-xl p-4 border border-blue-500/30">
                        <h3 class="text-lg font-semibold text-blue-300 mb-3 text-center">Person A</h3>
                        <select id="person-a-lang" class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300">
                            <option value="en">English</option>
                            <option value="es">Spanish</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="it">Italian</option>
                            <option value="pt">Portuguese</option>
                            <option value="ru">Russian</option>
                            <option value="ja">Japanese</option>
                            <option value="ko">Korean</option>
                            <option value="zh">Chinese</option>
                            <option value="ar">Arabic</option>
                            <option value="hi">Hindi</option>
                            <option value="nl">Dutch</option>
                            <option value="sv">Swedish</option>
                            <option value="da">Danish</option>
                            <option value="no">Norwegian</option>
                            <option value="fi">Finnish</option>
                            <option value="pl">Polish</option>
                            <option value="tr">Turkish</option>
                        </select>
                    </div>
                    <div class="bg-green-900/30 rounded-xl p-4 border border-green-500/30">
                        <h3 class="text-lg font-semibold text-green-300 mb-3 text-center">Person B</h3>
                        <select id="person-b-lang" class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none transition-all duration-300">
                            <option value="es">Spanish</option>
                            <option value="en">English</option>
                            <option value="fr">French</option>
                            <option value="de">German</option>
                            <option value="it">Italian</option>
                            <option value="pt">Portuguese</option>
                            <option value="ru">Russian</option>
                            <option value="ja">Japanese</option>
                            <option value="ko">Korean</option>
                            <option value="zh">Chinese</option>
                            <option value="ar">Arabic</option>
                            <option value="hi">Hindi</option>
                            <option value="nl">Dutch</option>
                            <option value="sv">Swedish</option>
                            <option value="da">Danish</option>
                            <option value="no">Norwegian</option>
                            <option value="fi">Finnish</option>
                            <option value="pl">Polish</option>
                            <option value="tr">Turkish</option>
                        </select>
                    </div>
                </div>

                <!-- Speaking Controls -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Person A Controls -->
                    <div id="person-a-controls" class="bg-blue-900/20 rounded-xl p-6 border border-blue-500/30">
                        <div class="text-center">
                            <h3 class="text-xl font-semibold text-blue-300 mb-4">Person A</h3>
                            <button
                                id="person-a-record-btn"
                                class="w-24 h-24 md:w-32 md:h-32 bg-blue-500 hover:bg-blue-600 active:bg-blue-700 rounded-full flex items-center justify-center transition-all duration-300 mx-auto mb-4 focus:ring-4 focus:ring-blue-300 focus:ring-opacity-50 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <svg class="w-8 h-8 md:w-12 md:h-12 text-white mic-icon" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                                </svg>
                                <svg class="w-8 h-8 md:w-12 md:h-12 text-white stop-icon hidden" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <p class="text-blue-300 text-sm mb-2 instruction">Tap to speak</p>
                            <div class="recording-indicator hidden">
                                <div class="flex items-center justify-center space-x-2 mb-2">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                                    <span class="text-blue-400 text-sm font-medium">Recording...</span>
                                </div>
                                <div class="recording-timer text-gray-400 text-xs">00:00</div>
                            </div>
                        </div>
                    </div>

                    <!-- Person B Controls -->
                    <div id="person-b-controls" class="bg-green-900/20 rounded-xl p-6 border border-green-500/30">
                        <div class="text-center">
                            <h3 class="text-xl font-semibold text-green-300 mb-4">Person B</h3>
                            <button
                                id="person-b-record-btn"
                                class="w-24 h-24 md:w-32 md:h-32 bg-green-500 hover:bg-green-600 active:bg-green-700 rounded-full flex items-center justify-center transition-all duration-300 mx-auto mb-4 focus:ring-4 focus:ring-green-300 focus:ring-opacity-50 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                                <svg class="w-8 h-8 md:w-12 md:h-12 text-white mic-icon" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                                </svg>
                                <svg class="w-8 h-8 md:w-12 md:h-12 text-white stop-icon hidden" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <p class="text-green-300 text-sm mb-2 instruction">Tap to speak</p>
                            <div class="recording-indicator hidden">
                                <div class="flex items-center justify-center space-x-2 mb-2">
                                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                    <span class="text-green-400 text-sm font-medium">Recording...</span>
                                </div>
                                <div class="recording-timer text-gray-400 text-xs">00:00</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Conversation History -->
                <div class="bg-gray-800 rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-300">Conversation</h3>
                        <button id="clear-conversation" class="text-gray-400 hover:text-red-400 text-sm transition-colors duration-300">
                            Clear
                        </button>
                    </div>
                    <div id="conversation-history" class="space-y-4 max-h-96 overflow-y-auto">
                        <div class="text-center text-gray-500 text-sm py-8">
                            Conversation will appear here...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Status -->
            <div class="mt-4 text-center">
                <div id="status" class="text-sm text-gray-400"></div>
            </div>
        </div>
    </div>

    <script src="translate.js"></script>
</body>
</html>
