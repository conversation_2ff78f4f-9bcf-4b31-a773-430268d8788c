<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Voice Translate</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    animation: {
                        'fade-in': 'fadeIn 0.6s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-in-out'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .typing-indicator::after {
            content: '|';
            animation: blink 1s infinite;
        }
        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Mobile optimizations */
        @media (max-width: 640px) {
            .record-button-mobile {
                width: 120px !important;
                height: 120px !important;
            }
            .record-button-mobile svg {
                width: 48px !important;
                height: 48px !important;
            }
        }

        /* Prevent zoom on input focus for iOS */
        @media screen and (-webkit-min-device-pixel-ratio: 0) {
            select, input, textarea {
                font-size: 16px !important;
            }
        }

        /* Touch-friendly buttons */
        button {
            -webkit-tap-highlight-color: transparent;
            touch-action: manipulation;
        }
    </style>
</head>
<body class="bg-gray-900 text-white min-h-screen font-sans">
    <!-- Auth Gate -->
    <div id="auth-gate" class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-gray-800 rounded-lg shadow-2xl p-8 w-full max-w-md animate-fade-in">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-blue-400 mb-2">Voice Translate</h1>
                <p class="text-gray-400">Enter access key to continue</p>
            </div>
            <form id="auth-form" class="space-y-6">
                <div>
                    <input 
                        type="password" 
                        id="access-key" 
                        placeholder="Access Key"
                        class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300"
                        required
                    >
                </div>
                <button 
                    type="submit"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-4 rounded-lg transition-colors duration-300 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800"
                >
                    Access
                </button>
                <div id="auth-error" class="text-red-400 text-sm text-center hidden"></div>
            </form>
        </div>
    </div>

    <!-- Translation UI -->
    <div id="translation-ui" class="hidden min-h-screen p-4">
        <div class="max-w-6xl mx-auto animate-slide-up">
            <!-- Header -->
            <header class="text-center mb-8">
                <h1 class="text-4xl font-bold text-blue-400 mb-2">Voice Translate</h1>
                <p class="text-gray-400">Real-time voice translation powered by AI</p>
            </header>

            <!-- Language Selectors -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Translate from</label>
                    <select id="source-lang" class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300">
                        <option value="auto">Auto-detect</option>
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="it">Italian</option>
                        <option value="pt">Portuguese</option>
                        <option value="ru">Russian</option>
                        <option value="ja">Japanese</option>
                        <option value="ko">Korean</option>
                        <option value="zh">Chinese</option>
                        <option value="ar">Arabic</option>
                        <option value="hi">Hindi</option>
                        <option value="nl">Dutch</option>
                        <option value="sv">Swedish</option>
                        <option value="da">Danish</option>
                        <option value="no">Norwegian</option>
                        <option value="fi">Finnish</option>
                        <option value="pl">Polish</option>
                        <option value="tr">Turkish</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-300 mb-2">Translate to</label>
                    <select id="target-lang" class="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all duration-300">
                        <option value="en">English</option>
                        <option value="es">Spanish</option>
                        <option value="fr">French</option>
                        <option value="de">German</option>
                        <option value="it">Italian</option>
                        <option value="pt">Portuguese</option>
                        <option value="ru">Russian</option>
                        <option value="ja">Japanese</option>
                        <option value="ko">Korean</option>
                        <option value="zh">Chinese</option>
                        <option value="ar">Arabic</option>
                        <option value="hi">Hindi</option>
                        <option value="nl">Dutch</option>
                        <option value="sv">Swedish</option>
                        <option value="da">Danish</option>
                        <option value="no">Norwegian</option>
                        <option value="fi">Finnish</option>
                        <option value="pl">Polish</option>
                        <option value="tr">Turkish</option>
                    </select>
                </div>
            </div>

            <!-- Voice Interface -->
            <div class="max-w-2xl mx-auto">
                <!-- Recording Section -->
                <div class="bg-gray-800 rounded-xl p-8 mb-6 text-center">
                    <div class="mb-6">
                        <button
                            id="record-btn"
                            class="w-32 h-32 record-button-mobile bg-red-500 hover:bg-red-600 active:bg-red-700 rounded-full flex items-center justify-center transition-all duration-300 mx-auto mb-4 focus:ring-4 focus:ring-red-300 focus:ring-opacity-50 shadow-lg"
                        >
                            <svg id="mic-icon" class="w-12 h-12 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clip-rule="evenodd"></path>
                            </svg>
                            <svg id="stop-icon" class="w-12 h-12 text-white hidden" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clip-rule="evenodd"></path>
                            </svg>
                        </button>
                        <div id="recording-indicator" class="hidden">
                            <div class="flex items-center justify-center space-x-2 mb-2">
                                <div class="w-3 h-3 bg-red-500 rounded-full animate-pulse"></div>
                                <span class="text-red-400 font-medium">Recording...</span>
                            </div>
                            <div id="recording-timer" class="text-gray-400 text-sm">00:00</div>
                        </div>
                    </div>
                    <p id="record-instruction" class="text-gray-400 mb-4">Tap to start recording</p>

                    <!-- Audio Visualization -->
                    <div id="audio-visualizer" class="hidden mb-4">
                        <div class="flex items-center justify-center space-x-1 h-16">
                            <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 20px;"></div>
                            <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 40px; animation-delay: 0.1s;"></div>
                            <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 30px; animation-delay: 0.2s;"></div>
                            <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 50px; animation-delay: 0.3s;"></div>
                            <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 25px; animation-delay: 0.4s;"></div>
                            <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 45px; animation-delay: 0.5s;"></div>
                            <div class="w-1 bg-blue-400 rounded-full animate-pulse" style="height: 35px; animation-delay: 0.6s;"></div>
                        </div>
                    </div>
                </div>

                <!-- Transcription Display -->
                <div id="transcription-section" class="bg-gray-800 rounded-xl p-6 mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-300 mb-3">What you said:</h3>
                    <div id="transcription-text" class="text-white bg-gray-700 rounded-lg p-4 min-h-[60px]"></div>
                </div>

                <!-- Translation Display -->
                <div id="translation-section" class="bg-gray-800 rounded-xl p-6 mb-6 hidden">
                    <h3 class="text-lg font-semibold text-gray-300 mb-3">Translation:</h3>
                    <div id="translation-text" class="text-white bg-gray-700 rounded-lg p-4 min-h-[60px] mb-4"></div>

                    <!-- Play Translation Button -->
                    <button
                        id="play-translation-btn"
                        class="w-full bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300 flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled
                    >
                        <svg id="play-icon" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                        </svg>
                        <svg id="pause-icon" class="w-5 h-5 hidden" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zM7 8a1 1 0 012 0v4a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v4a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span id="play-btn-text">Play Translation</span>
                    </button>
                </div>
            </div>

            <!-- Status -->
            <div class="mt-4 text-center">
                <div id="status" class="text-sm text-gray-400"></div>
            </div>
        </div>
    </div>

    <script src="translate.js"></script>
</body>
</html>
