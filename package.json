{"name": "livetranslate", "version": "1.0.0", "description": "Live text translation web app with OpenAI integration", "main": "index.html", "scripts": {"dev": "node server.js", "build": "echo 'No build step required'", "start": "node server.js"}, "keywords": ["translation", "openai", "live", "streaming"], "author": "", "license": "MIT", "devDependencies": {"vercel": "^32.0.0"}, "dependencies": {"dotenv": "^16.0.0"}}