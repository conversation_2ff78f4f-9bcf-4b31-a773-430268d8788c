0.2.2 / 2016-09-05
==================

* [[`764f2420ef`](https://github.com/TooTallNate/stat-mode/commit/764f2420ef)] - Rename `define()` to `_define()` (#6) (CxRes)
* [[`ba5a88d6e4`](https://github.com/TooTallNate/stat-mode/commit/ba5a88d6e4)] - **package**: update "mocha" to v3.0.2 (<PERSON>)
* [[`e8a4a487ab`](https://github.com/TooTallNate/stat-mode/commit/e8a4a487ab)] - **travis**: test lots more node versions (<PERSON>)

0.2.1 / 2015-04-06
==================

  * fix #2
  * add regression test for #2
  * travis: test node v0.12 instead of v0.11
  * .travis: don't test node v0.9.x
  * add .travis.yml file
  * README: add Travis-CI badge
  * test: add FIFO test
  * test: more test cases
  * test: more inputs for tests
  * package: update "mocha" to v1.18.2
  * package: add a few more "keywords"

0.2.0 / 2014-04-02
==================

  * index: add `Mode#toOctal()` function
  * index: add `setuid`, `setgid` and `sticky` props
  * test: initial tests

0.1.0 / 2014-03-01
==================

  * package: remove the "test" script for now
  * index: add `Mode#toString()` function
  * index: add `Mode#valueOf()` function

0.0.1 / 2014-03-01
==================

  * initial commit
