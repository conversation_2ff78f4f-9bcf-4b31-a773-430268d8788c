{"name": "@vercel/build-utils", "version": "7.3.0", "license": "Apache-2.0", "main": "./dist/index.js", "types": "./dist/index.d.js", "homepage": "https://github.com/vercel/vercel/blob/main/DEVELOPING_A_RUNTIME.md", "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/now-build-utils"}, "devDependencies": {"@iarna/toml": "2.2.3", "@types/async-retry": "^1.2.1", "@types/cross-spawn": "6.0.0", "@types/end-of-stream": "^1.4.0", "@types/fs-extra": "9.0.13", "@types/glob": "7.2.0", "@types/jest": "27.4.1", "@types/js-yaml": "3.12.1", "@types/minimatch": "^5.1.2", "@types/ms": "0.7.31", "@types/multistream": "2.1.1", "@types/node": "14.18.33", "@types/node-fetch": "^2.1.6", "@types/semver": "6.0.0", "@types/yazl": "2.4.2", "@vercel/error-utils": "2.0.2", "aggregate-error": "3.0.1", "async-retry": "1.2.3", "async-sema": "2.1.4", "cross-spawn": "6.0.5", "end-of-stream": "1.4.1", "execa": "3.2.0", "fs-extra": "10.0.0", "glob": "8.0.3", "ignore": "4.0.6", "into-stream": "5.0.0", "jest-junit": "16.0.0", "js-yaml": "3.13.1", "minimatch": "3.1.2", "multistream": "2.1.1", "node-fetch": "2.6.7", "semver": "6.3.1", "typescript": "4.9.5", "yazl": "2.5.1"}, "scripts": {"build": "node build.mjs", "test": "jest --reporters=default --reporters=jest-junit --env node --verbose --runInBand --bail", "test-unit": "pnpm test test/unit.*test.*", "test-e2e": "pnpm test test/integration.test.ts", "type-check": "tsc --noEmit"}}