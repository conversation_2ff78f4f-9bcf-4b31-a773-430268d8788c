// Rate limiting storage (in-memory for simplicity)
const rateLimits = new Map();

// Simple rate limiter: 30 requests per minute per IP
function checkRateLimit(ip) {
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute
    const maxRequests = 30;

    if (!rateLimits.has(ip)) {
        rateLimits.set(ip, []);
    }

    const requests = rateLimits.get(ip);
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= maxRequests) {
        return false;
    }

    validRequests.push(now);
    rateLimits.set(ip, validRequests);
    return true;
}

// Generate simple auth token
function generateAuthToken() {
    return Buffer.from(`${Date.now()}-${Math.random()}`).toString('base64');
}

// Verify auth token (simple implementation)
function verifyAuthToken(token) {
    try {
        const decoded = Buffer.from(token, 'base64').toString();
        const [timestamp] = decoded.split('-');
        const tokenAge = Date.now() - parseInt(timestamp);
        
        // Token valid for 24 hours
        return tokenAge < 24 * 60 * 60 * 1000;
    } catch {
        return false;
    }
}

export default async function handler(req, res) {
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        return res.status(200).end();
    }

    if (req.method !== 'POST') {
        return res.status(405).json({ error: 'Method not allowed' });
    }

    // Rate limiting
    const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';
    if (!checkRateLimit(clientIp)) {
        return res.status(429).json({ error: 'Rate limit exceeded. Please try again later.' });
    }

    try {
        const { action, accessKey, text, sourceLang, targetLang } = req.body;

        // Handle authentication
        if (action === 'auth') {
            const adminKey = process.env.ADMIN_KEY;
            
            if (!adminKey) {
                return res.status(500).json({ error: 'Server configuration error' });
            }

            if (accessKey === adminKey) {
                const token = generateAuthToken();
                return res.status(200).json({ 
                    success: true, 
                    token: token 
                });
            } else {
                return res.status(401).json({ 
                    success: false, 
                    error: 'Invalid access key' 
                });
            }
        }

        // Handle voice translation
        if (action === 'voice-translate') {
            return await handleVoiceTranslation(req, res);
        }

        // Handle text-to-speech
        if (action === 'text-to-speech') {
            return await handleTextToSpeech(req, res);
        }

        // Handle text translation (legacy)
        if (action === 'translate') {
            return await handleTextTranslation(req, res);
        }

        return res.status(400).json({ error: 'Invalid action' });

    } catch (error) {
        console.error('Handler error:', error);
        return res.status(500).json({ error: 'Internal server error' });
    }
}

// Handle voice translation (speech-to-text + translation)
async function handleVoiceTranslation(req, res) {
    // Verify auth token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = authHeader.slice(7);
    if (!verifyAuthToken(token)) {
        return res.status(401).json({ error: 'Invalid or expired token' });
    }

    const { audioData, sourceLang, targetLang } = req.body;

    if (!audioData || !targetLang) {
        return res.status(400).json({ error: 'Missing required parameters' });
    }

    const openaiApiKey = process.env.OPENAI_API_KEY;
    if (!openaiApiKey) {
        return res.status(500).json({ error: 'OpenAI API key not configured' });
    }

    try {
        // Convert base64 audio to buffer
        const audioBuffer = Buffer.from(audioData, 'base64');

        // Create form data for Whisper API
        const formData = new FormData();
        formData.append('file', new Blob([audioBuffer], { type: 'audio/webm' }), 'audio.webm');
        formData.append('model', 'whisper-1');
        if (sourceLang !== 'auto') {
            formData.append('language', sourceLang);
        }

        // Transcribe with Whisper
        const transcriptionResponse = await fetch('https://api.openai.com/v1/audio/transcriptions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${openaiApiKey}`,
            },
            body: formData
        });

        if (!transcriptionResponse.ok) {
            const errorData = await transcriptionResponse.json();
            console.error('Whisper API error:', errorData);
            return res.status(500).json({ error: 'Speech recognition failed' });
        }

        const transcriptionData = await transcriptionResponse.json();
        const transcribedText = transcriptionData.text;

        if (!transcribedText || transcribedText.trim().length === 0) {
            return res.status(400).json({ error: 'No speech detected in audio' });
        }

        // Translate the transcribed text
        const sourceLanguage = sourceLang === 'auto' ? 'the detected language' : getLanguageName(sourceLang);
        const targetLanguage = getLanguageName(targetLang);

        const systemMessage = `You are a professional translator. Translate the user's text from ${sourceLanguage} to ${targetLanguage}. Preserve formatting and proper nouns. Only return the translation, no explanations.`;

        const translationResponse = await fetch('https://api.openai.com/v1/chat/completions', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${openaiApiKey}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: 'gpt-4o',
                messages: [
                    { role: 'system', content: systemMessage },
                    { role: 'user', content: transcribedText }
                ],
                temperature: 0.3,
                max_tokens: 2000
            })
        });

        if (!translationResponse.ok) {
            const errorData = await translationResponse.json();
            console.error('Translation API error:', errorData);
            return res.status(500).json({ error: 'Translation failed' });
        }

        const translationData = await translationResponse.json();
        const translatedText = translationData.choices[0].message.content;

        return res.status(200).json({
            transcription: transcribedText,
            translation: translatedText
        });

    } catch (error) {
        console.error('Voice translation error:', error);
        return res.status(500).json({ error: 'Voice translation failed' });
    }
}

// Handle text-to-speech
async function handleTextToSpeech(req, res) {
    // Verify auth token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = authHeader.slice(7);
    if (!verifyAuthToken(token)) {
        return res.status(401).json({ error: 'Invalid or expired token' });
    }

    const { text, language } = req.body;

    if (!text) {
        return res.status(400).json({ error: 'Missing text parameter' });
    }

    const openaiApiKey = process.env.OPENAI_API_KEY;
    if (!openaiApiKey) {
        return res.status(500).json({ error: 'OpenAI API key not configured' });
    }

    try {
        // Map language codes to TTS voices
        const voiceMap = {
            'en': 'alloy',
            'es': 'nova',
            'fr': 'shimmer',
            'de': 'echo',
            'it': 'fable',
            'pt': 'onyx',
            'ru': 'alloy',
            'ja': 'nova',
            'ko': 'shimmer',
            'zh': 'echo',
            'ar': 'fable',
            'hi': 'onyx',
            'nl': 'alloy',
            'sv': 'nova',
            'da': 'shimmer',
            'no': 'echo',
            'fi': 'fable',
            'pl': 'onyx',
            'tr': 'alloy'
        };

        const voice = voiceMap[language] || 'alloy';

        const ttsResponse = await fetch('https://api.openai.com/v1/audio/speech', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${openaiApiKey}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: 'tts-1',
                input: text,
                voice: voice,
                response_format: 'mp3'
            })
        });

        if (!ttsResponse.ok) {
            const errorData = await ttsResponse.json();
            console.error('TTS API error:', errorData);
            return res.status(500).json({ error: 'Text-to-speech failed' });
        }

        // Stream the audio response
        res.setHeader('Content-Type', 'audio/mpeg');
        res.setHeader('Cache-Control', 'no-cache');

        const reader = ttsResponse.body.getReader();

        try {
            while (true) {
                const { done, value } = await reader.read();

                if (done) break;

                res.write(value);
            }
        } finally {
            res.end();
        }

    } catch (error) {
        console.error('TTS error:', error);
        return res.status(500).json({ error: 'Text-to-speech failed' });
    }
}

// Handle legacy text translation
async function handleTextTranslation(req, res) {
    // Verify auth token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ error: 'Unauthorized' });
    }

    const token = authHeader.slice(7);
    if (!verifyAuthToken(token)) {
        return res.status(401).json({ error: 'Invalid or expired token' });
    }

    const { text, sourceLang, targetLang } = req.body;

    // Validate OpenAI API key
    const openaiApiKey = process.env.OPENAI_API_KEY;
    if (!openaiApiKey) {
        return res.status(500).json({ error: 'OpenAI API key not configured' });
    }

    // Validate input
    if (!text || !targetLang) {
        return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Prepare system message
    const sourceLanguage = sourceLang === 'auto' ? 'the detected language' : getLanguageName(sourceLang);
    const targetLanguage = getLanguageName(targetLang);

    const systemMessage = `You are a professional translator. Translate the user's text from ${sourceLanguage} to ${targetLanguage}. Preserve formatting and proper nouns. Only return the translation, no explanations.`;

    // Call OpenAI API with streaming
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${openaiApiKey}`,
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            model: 'gpt-4o',
            messages: [
                { role: 'system', content: systemMessage },
                { role: 'user', content: text }
            ],
            stream: true,
            temperature: 0.3,
            max_tokens: 2000
        })
    });

    if (!openaiResponse.ok) {
        const errorData = await openaiResponse.json();
        console.error('OpenAI API error:', errorData);
        return res.status(500).json({ error: 'Translation service error' });
    }

    // Set up streaming response
    res.setHeader('Content-Type', 'text/plain; charset=utf-8');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');

    // Stream the response
    const reader = openaiResponse.body.getReader();
    const decoder = new TextDecoder();

    try {
        while (true) {
            const { done, value } = await reader.read();

            if (done) {
                res.write('data: [DONE]\n\n');
                break;
            }

            const chunk = decoder.decode(value, { stream: true });
            res.write(chunk);
        }
    } catch (streamError) {
        console.error('Streaming error:', streamError);
        res.write('data: {"error": "Streaming error"}\n\n');
    } finally {
        res.end();
    }
}

// Helper function to get language names
function getLanguageName(code) {
    const languages = {
        'auto': 'auto-detected language',
        'en': 'English',
        'es': 'Spanish',
        'fr': 'French',
        'de': 'German',
        'it': 'Italian',
        'pt': 'Portuguese',
        'ru': 'Russian',
        'ja': 'Japanese',
        'ko': 'Korean',
        'zh': 'Chinese',
        'ar': 'Arabic',
        'hi': 'Hindi',
        'nl': 'Dutch',
        'sv': 'Swedish',
        'da': 'Danish',
        'no': 'Norwegian',
        'fi': 'Finnish',
        'pl': 'Polish',
        'tr': 'Turkish'
    };

    return languages[code] || code;
}
