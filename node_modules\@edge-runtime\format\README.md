<div align="center">
  <br>
  <img src="https://user-images.githubusercontent.com/2096101/235130063-e561514e-1f66-4ff6-9034-70dbf7ca3260.png#gh-dark-mode-only">
  <img src="https://user-images.githubusercontent.com/2096101/235127419-ac6fe609-d0cd-4339-a593-c48305a83823.png#gh-light-mode-only">
  <br>
  <br>
  <p align="center"><strong>@edge-runtime/format</strong>: An <a href="https://nodejs.org/api/util.html#utilinspectobject-showhidden-depth-colors" target='_blank' rel='noopener noreferrer'>util.inspect</a> implementation to serialize any value.</p>
  <p align="center">See <a href="https://edge-runtime.vercel.app/packages/primitives" target='_blank' rel='noopener noreferrer'>@edge-runtime/format</a> section in our <a href="https://edge-runtime.vercel.app/" target='_blank' rel='noopener noreferrer'>website</a> for more information.</p>
  <br>
</div>

## Install

Using npm:

```sh
npm install @edge-runtime/format --save
```

or using yarn:

```sh
yarn add @edge-runtime/format --dev
```

or using pnpm:

```sh
pnpm install @edge-runtime/format --save
```

## License

**@edge-runtime/format** © [Vercel](https://vercel.com), released under the [MPLv2](https://github.com/vercel/edge-runtime/blob/main/LICENSE.md) License, based on [Node.js source code](https://github.com/nodejs/node/blob/v18.7.0/lib/util.js).<br>
Authored and maintained by [Vercel](https://vercel.com) with help from [contributors](https://github.com/vercel/edge-runtime/contributors).

> [vercel.com](https://vercel.com) · GitHub [Vercel](https://github.com/vercel) · Twitter [@vercel](https://twitter.com/vercel)
