{"name": "promisepipe", "version": "3.0.0", "description": "Pipe node.js streams safely with Promises", "main": "index.js", "scripts": {"test": "mocha test.js"}, "repository": {"type": "git", "url": "https://github.com/epeli/node-promisepipe"}, "keywords": ["promise", "stream"], "author": "Esa<PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/epeli/node-promisepipe/issues"}, "devDependencies": {"mocha": "^3.2.0", "mz": "^2.6.0"}, "dependencies": {}}