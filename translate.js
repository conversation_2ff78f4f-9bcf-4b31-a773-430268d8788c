class ConversationTranslator {
    constructor() {
        this.isAuthenticated = false;
        this.activeRecorder = null; // 'person-a' or 'person-b' or null
        this.mediaRecorders = {
            'person-a': null,
            'person-b': null
        };
        this.audioChunks = {
            'person-a': [],
            'person-b': []
        };
        this.recordingTimers = {
            'person-a': null,
            'person-b': null
        };
        this.recordingStartTimes = {
            'person-a': null,
            'person-b': null
        };
        this.currentAudio = null;
        this.conversationHistory = [];
        this.autoPlayEnabled = true; // Default to auto-play enabled
        this.init();
    }

    init() {
        this.bindEvents();
        this.setDefaultLanguages();
        this.checkBrowserSupport();
        this.checkCachedAuth();
    }

    checkBrowserSupport() {
        // Check for required APIs
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            this.showError('Your browser does not support audio recording. Please use a modern browser.');
            return false;
        }
        return true;
    }

    bindEvents() {
        // Auth form
        document.getElementById('auth-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.authenticate();
        });

        // Record buttons
        document.getElementById('person-a-record-btn').addEventListener('click', () => {
            this.toggleRecording('person-a');
        });

        document.getElementById('person-b-record-btn').addEventListener('click', () => {
            this.toggleRecording('person-b');
        });

        // Clear conversation
        document.getElementById('clear-conversation').addEventListener('click', () => {
            this.clearConversation();
        });

        // Logout button
        document.getElementById('logout-btn').addEventListener('click', () => {
            this.logout();
        });

        // Auto-play toggle
        document.getElementById('auto-play-toggle').addEventListener('change', (e) => {
            this.autoPlayEnabled = e.target.checked;
            this.updateStatus(this.autoPlayEnabled ? 'Auto-play enabled' : 'Auto-play disabled');
        });

        // Pause all audio button
        document.getElementById('pause-all-audio').addEventListener('click', () => {
            this.pauseAllAudio();
        });

        // Language changes
        document.getElementById('person-a-lang').addEventListener('change', () => {
            this.updateStatus('Language settings updated');
        });

        document.getElementById('person-b-lang').addEventListener('change', () => {
            this.updateStatus('Language settings updated');
        });
    }

    setDefaultLanguages() {
        document.getElementById('person-a-lang').value = 'nl';
        document.getElementById('person-b-lang').value = 'tr';
    }

    clearConversation() {
        this.conversationHistory = [];
        const historyDiv = document.getElementById('conversation-history');
        historyDiv.innerHTML = '<div class="text-center text-gray-500 text-sm py-8">Conversation will appear here...</div>';
        this.updateStatus('Conversation cleared');
    }

    checkCachedAuth() {
        const cachedAuth = localStorage.getItem('voiceTranslateAuth');

        if (cachedAuth) {
            try {
                const authData = JSON.parse(cachedAuth);
                const now = Date.now();

                // Check if cached auth is still valid (1 hour = 3600000 ms)
                if (authData.timestamp && (now - authData.timestamp) < 3600000) {
                    // Auth is still valid
                    this.isAuthenticated = true;
                    sessionStorage.setItem('authToken', authData.token);
                    this.showTranslationUI();
                    this.requestMicrophonePermission();
                    this.updateStatus('Welcome back! Using cached authentication.');
                    return;
                }
            } catch (error) {
                console.error('Error parsing cached auth:', error);
            }
        }

        // Clear invalid or expired cached auth
        localStorage.removeItem('voiceTranslateAuth');
    }

    saveCachedAuth(token) {
        const authData = {
            token: token,
            timestamp: Date.now()
        };
        localStorage.setItem('voiceTranslateAuth', JSON.stringify(authData));
    }

    clearCachedAuth() {
        localStorage.removeItem('voiceTranslateAuth');
        sessionStorage.removeItem('authToken');
    }

    logout() {
        // Stop any ongoing recording
        if (this.activeRecorder) {
            this.stopRecording(this.activeRecorder);
        }

        // Stop any playing audio
        if (this.currentAudio && !this.currentAudio.paused) {
            this.currentAudio.pause();
            this.currentAudio = null;
        }

        // Clear authentication
        this.isAuthenticated = false;
        this.clearCachedAuth();

        // Clear conversation
        this.clearConversation();

        // Show auth gate
        document.getElementById('translation-ui').classList.add('hidden');
        document.getElementById('auth-gate').classList.remove('hidden');

        // Clear access key input
        document.getElementById('access-key').value = '';

        this.updateStatus('Logged out successfully');
    }

    pauseAllAudio() {
        if (this.currentAudio && !this.currentAudio.paused) {
            this.currentAudio.pause();
            this.currentAudio = null;
            this.updateStatus('Audio paused');
            document.getElementById('pause-all-audio').classList.add('hidden');
        }
    }

    playConversationEntry(index) {
        const entry = this.conversationHistory[index];
        if (entry && entry.translatedText) {
            this.playTranslationAudio(entry.translatedText, entry.targetLang);
        }
    }

    async authenticate() {
        const accessKey = document.getElementById('access-key').value;

        if (!accessKey.trim()) {
            this.showAuthError('Please enter an access key');
            return;
        }

        try {
            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'auth',
                    accessKey: accessKey
                })
            });

            const data = await response.json();

            if (data.success) {
                this.isAuthenticated = true;
                this.showTranslationUI();
                // Store auth token for subsequent requests
                sessionStorage.setItem('authToken', data.token);
                // Cache the authentication for 1 hour
                this.saveCachedAuth(data.token);
                // Request microphone permission
                await this.requestMicrophonePermission();
            } else {
                this.showAuthError(data.error || 'Invalid access key');
            }
        } catch (error) {
            console.error('Auth error:', error);
            this.showAuthError('Connection error. Please try again.');
        }
    }

    async requestMicrophonePermission() {
        try {
            await navigator.mediaDevices.getUserMedia({ audio: true });
            this.updateStatus('Microphone access granted. Ready to record!');
        } catch (error) {
            console.error('Microphone permission error:', error);
            this.showError('Microphone access is required for voice translation. Please allow microphone access and refresh the page.');
        }
    }

    showAuthError(message) {
        const errorDiv = document.getElementById('auth-error');
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
        
        // Clear error after 5 seconds
        setTimeout(() => {
            errorDiv.classList.add('hidden');
        }, 5000);
    }

    showTranslationUI() {
        document.getElementById('auth-gate').classList.add('hidden');
        document.getElementById('translation-ui').classList.remove('hidden');
    }

    async toggleRecording(person) {
        if (this.activeRecorder === person) {
            await this.stopRecording(person);
        } else if (this.activeRecorder === null) {
            await this.startRecording(person);
        } else {
            // Another person is recording, show message
            this.updateStatus('Please wait for the other person to finish speaking');
        }
    }

    async startRecording(person) {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 44100
                }
            });

            this.mediaRecorders[person] = new MediaRecorder(stream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.audioChunks[person] = [];
            this.activeRecorder = person;
            this.recordingStartTimes[person] = Date.now();

            this.mediaRecorders[person].ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks[person].push(event.data);
                }
            };

            this.mediaRecorders[person].onstop = () => {
                this.processRecording(person);
            };

            this.mediaRecorders[person].start();
            this.updateRecordingUI(person, true);
            this.startRecordingTimer(person);
            this.updateStatus(`${person === 'person-a' ? 'Person A' : 'Person B'} is speaking...`);

            // Disable the other person's button
            const otherPerson = person === 'person-a' ? 'person-b' : 'person-a';
            document.getElementById(`${otherPerson}-record-btn`).disabled = true;

        } catch (error) {
            console.error('Recording error:', error);
            this.showError('Failed to start recording. Please check microphone permissions.');
        }
    }

    async stopRecording(person) {
        if (this.mediaRecorders[person] && this.activeRecorder === person) {
            this.mediaRecorders[person].stop();
            this.mediaRecorders[person].stream.getTracks().forEach(track => track.stop());
            this.activeRecorder = null;
            this.updateRecordingUI(person, false);
            this.stopRecordingTimer(person);
            this.updateStatus('Processing audio...');

            // Re-enable the other person's button
            const otherPerson = person === 'person-a' ? 'person-b' : 'person-a';
            document.getElementById(`${otherPerson}-record-btn`).disabled = false;
        }
    }

    updateRecordingUI(person, isRecording) {
        const controls = document.getElementById(`${person}-controls`);
        const recordBtn = document.getElementById(`${person}-record-btn`);
        const micIcon = controls.querySelector('.mic-icon');
        const stopIcon = controls.querySelector('.stop-icon');
        const recordingIndicator = controls.querySelector('.recording-indicator');
        const instruction = controls.querySelector('.instruction');

        if (isRecording) {
            recordBtn.classList.add('animate-pulse');
            micIcon.classList.add('hidden');
            stopIcon.classList.remove('hidden');
            recordingIndicator.classList.remove('hidden');
            instruction.textContent = 'Tap to stop';
        } else {
            recordBtn.classList.remove('animate-pulse');
            micIcon.classList.remove('hidden');
            stopIcon.classList.add('hidden');
            recordingIndicator.classList.add('hidden');
            instruction.textContent = 'Tap to speak';
        }
    }

    startRecordingTimer(person) {
        this.recordingTimers[person] = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.recordingStartTimes[person]) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            const controls = document.getElementById(`${person}-controls`);
            const timerElement = controls.querySelector('.recording-timer');
            timerElement.textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopRecordingTimer(person) {
        if (this.recordingTimers[person]) {
            clearInterval(this.recordingTimers[person]);
            this.recordingTimers[person] = null;
        }
    }

    async processRecording(person) {
        try {
            const audioBlob = new Blob(this.audioChunks[person], { type: 'audio/webm' });

            // Convert to base64 for API transmission
            const arrayBuffer = await audioBlob.arrayBuffer();
            const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

            await this.transcribeAndTranslate(person, base64Audio);

        } catch (error) {
            console.error('Processing error:', error);
            this.showError('Failed to process recording. Please try again.');
        }
    }

    async transcribeAndTranslate(person, audioData) {
        const sourceLang = document.getElementById(`${person}-lang`).value;
        const otherPerson = person === 'person-a' ? 'person-b' : 'person-a';
        const targetLang = document.getElementById(`${otherPerson}-lang`).value;
        const authToken = sessionStorage.getItem('authToken');

        this.updateStatus('Transcribing speech...');

        try {
            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    action: 'voice-translate',
                    audioData: audioData,
                    sourceLang: sourceLang,
                    targetLang: targetLang
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.transcription && data.translation) {
                // Add to conversation history
                this.addToConversation(person, data.transcription, data.translation, sourceLang, targetLang);

                // Auto-play translation if enabled
                if (this.autoPlayEnabled) {
                    await this.playTranslationAudio(data.translation, targetLang);
                    this.updateStatus('Translation complete');
                } else {
                    this.updateStatus('Translation complete - Auto-play disabled');
                }
            } else {
                throw new Error('No transcription or translation received');
            }

        } catch (error) {
            console.error('Transcription error:', error);
            this.showError('Failed to transcribe audio. Please try again.');
        }
    }

    addToConversation(speaker, originalText, translatedText, sourceLang, targetLang) {
        const timestamp = new Date().toLocaleTimeString();
        const entry = {
            speaker,
            originalText,
            translatedText,
            sourceLang,
            targetLang,
            timestamp
        };

        this.conversationHistory.push(entry);
        this.updateConversationDisplay();
    }

    updateConversationDisplay() {
        const historyDiv = document.getElementById('conversation-history');

        if (this.conversationHistory.length === 0) {
            historyDiv.innerHTML = '<div class="text-center text-gray-500 text-sm py-8">Conversation will appear here...</div>';
            return;
        }

        const html = this.conversationHistory.map((entry, index) => {
            const speakerColor = entry.speaker === 'person-a' ? 'blue' : 'green';
            const speakerName = entry.speaker === 'person-a' ? 'Person A' : 'Person B';

            return `
                <div class="border-l-4 border-${speakerColor}-500 pl-4 py-3">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-${speakerColor}-300 font-semibold text-sm">${speakerName}</span>
                        <div class="flex items-center space-x-2">
                            <button
                                onclick="window.conversationTranslator.playConversationEntry(${index})"
                                class="text-gray-400 hover:text-${speakerColor}-400 transition-colors duration-300 p-1 rounded"
                                title="Play translation"
                            >
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                            <span class="text-gray-500 text-xs">${entry.timestamp}</span>
                        </div>
                    </div>
                    <div class="text-white mb-2">"${entry.originalText}"</div>
                    <div class="text-gray-300 text-sm italic">→ "${entry.translatedText}"</div>
                </div>
            `;
        }).join('');

        historyDiv.innerHTML = html;

        // Scroll to bottom
        historyDiv.scrollTop = historyDiv.scrollHeight;
    }

    async playTranslationAudio(text, language) {
        if (!text) return;

        try {
            // Stop current audio if playing
            if (this.currentAudio && !this.currentAudio.paused) {
                this.currentAudio.pause();
                this.currentAudio = null;
            }

            const authToken = sessionStorage.getItem('authToken');

            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    action: 'text-to-speech',
                    text: text,
                    language: language
                })
            });

            if (!response.ok) {
                throw new Error('Failed to generate speech');
            }

            const audioBlob = await response.blob();
            const audioUrl = URL.createObjectURL(audioBlob);

            this.currentAudio = new Audio(audioUrl);

            return new Promise((resolve, reject) => {
                this.currentAudio.onloadeddata = () => {
                    this.currentAudio.play();
                    // Show pause button when audio starts playing
                    document.getElementById('pause-all-audio').classList.remove('hidden');
                };

                this.currentAudio.onended = () => {
                    URL.revokeObjectURL(audioUrl);
                    // Hide pause button when audio ends
                    document.getElementById('pause-all-audio').classList.add('hidden');
                    resolve();
                };

                this.currentAudio.onerror = () => {
                    URL.revokeObjectURL(audioUrl);
                    // Hide pause button on error
                    document.getElementById('pause-all-audio').classList.add('hidden');
                    reject(new Error('Audio playback failed'));
                };
            });

        } catch (error) {
            console.error('TTS error:', error);
            this.showError('Failed to play translation audio.');
        }
    }

    showError(message) {
        this.updateStatus(`Error: ${message}`);
        // You could also show a toast notification here
    }

    updateStatus(message) {
        document.getElementById('status').textContent = message;
    }
}

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    window.conversationTranslator = new ConversationTranslator();
});
