class VoiceTranslator {
    constructor() {
        this.isAuthenticated = false;
        this.isRecording = false;
        this.mediaRecorder = null;
        this.audioChunks = [];
        this.currentController = null;
        this.recordingStartTime = null;
        this.recordingTimer = null;
        this.currentAudio = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.setDefaultLanguages();
        this.checkBrowserSupport();
    }

    checkBrowserSupport() {
        // Check for required APIs
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
            this.showError('Your browser does not support audio recording. Please use a modern browser.');
            return false;
        }
        return true;
    }

    bindEvents() {
        // Auth form
        document.getElementById('auth-form').addEventListener('submit', (e) => {
            e.preventDefault();
            this.authenticate();
        });

        // Record button
        document.getElementById('record-btn').addEventListener('click', () => {
            this.toggleRecording();
        });

        // Play translation button
        document.getElementById('play-translation-btn').addEventListener('click', () => {
            this.playTranslation();
        });

        // Language change
        document.getElementById('source-lang').addEventListener('change', () => {
            this.clearResults();
        });

        document.getElementById('target-lang').addEventListener('change', () => {
            this.clearResults();
        });
    }

    setDefaultLanguages() {
        document.getElementById('source-lang').value = 'auto';
        document.getElementById('target-lang').value = 'es';
    }

    clearResults() {
        document.getElementById('transcription-section').classList.add('hidden');
        document.getElementById('translation-section').classList.add('hidden');
        document.getElementById('transcription-text').textContent = '';
        document.getElementById('translation-text').textContent = '';
        document.getElementById('play-translation-btn').disabled = true;
    }

    async authenticate() {
        const accessKey = document.getElementById('access-key').value;

        if (!accessKey.trim()) {
            this.showAuthError('Please enter an access key');
            return;
        }

        try {
            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'auth',
                    accessKey: accessKey
                })
            });

            const data = await response.json();

            if (data.success) {
                this.isAuthenticated = true;
                this.showTranslationUI();
                // Store auth token for subsequent requests
                sessionStorage.setItem('authToken', data.token);
                // Request microphone permission
                await this.requestMicrophonePermission();
            } else {
                this.showAuthError(data.error || 'Invalid access key');
            }
        } catch (error) {
            console.error('Auth error:', error);
            this.showAuthError('Connection error. Please try again.');
        }
    }

    async requestMicrophonePermission() {
        try {
            await navigator.mediaDevices.getUserMedia({ audio: true });
            this.updateStatus('Microphone access granted. Ready to record!');
        } catch (error) {
            console.error('Microphone permission error:', error);
            this.showError('Microphone access is required for voice translation. Please allow microphone access and refresh the page.');
        }
    }

    showAuthError(message) {
        const errorDiv = document.getElementById('auth-error');
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
        
        // Clear error after 5 seconds
        setTimeout(() => {
            errorDiv.classList.add('hidden');
        }, 5000);
    }

    showTranslationUI() {
        document.getElementById('auth-gate').classList.add('hidden');
        document.getElementById('translation-ui').classList.remove('hidden');
    }

    async toggleRecording() {
        if (this.isRecording) {
            await this.stopRecording();
        } else {
            await this.startRecording();
        }
    }

    async startRecording() {
        try {
            this.clearResults();

            const stream = await navigator.mediaDevices.getUserMedia({
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    sampleRate: 44100
                }
            });

            this.mediaRecorder = new MediaRecorder(stream, {
                mimeType: 'audio/webm;codecs=opus'
            });

            this.audioChunks = [];
            this.isRecording = true;
            this.recordingStartTime = Date.now();

            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.audioChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.processRecording();
            };

            this.mediaRecorder.start();
            this.updateRecordingUI(true);
            this.startRecordingTimer();
            this.updateStatus('Recording... Speak now!');

        } catch (error) {
            console.error('Recording error:', error);
            this.showError('Failed to start recording. Please check microphone permissions.');
        }
    }

    async stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
            this.isRecording = false;
            this.updateRecordingUI(false);
            this.stopRecordingTimer();
            this.updateStatus('Processing audio...');
        }
    }

    updateRecordingUI(isRecording) {
        const recordBtn = document.getElementById('record-btn');
        const micIcon = document.getElementById('mic-icon');
        const stopIcon = document.getElementById('stop-icon');
        const recordingIndicator = document.getElementById('recording-indicator');
        const audioVisualizer = document.getElementById('audio-visualizer');
        const instruction = document.getElementById('record-instruction');

        if (isRecording) {
            recordBtn.classList.remove('bg-red-500', 'hover:bg-red-600');
            recordBtn.classList.add('bg-red-600', 'hover:bg-red-700', 'animate-pulse');
            micIcon.classList.add('hidden');
            stopIcon.classList.remove('hidden');
            recordingIndicator.classList.remove('hidden');
            audioVisualizer.classList.remove('hidden');
            instruction.textContent = 'Tap to stop recording';
        } else {
            recordBtn.classList.remove('bg-red-600', 'hover:bg-red-700', 'animate-pulse');
            recordBtn.classList.add('bg-red-500', 'hover:bg-red-600');
            micIcon.classList.remove('hidden');
            stopIcon.classList.add('hidden');
            recordingIndicator.classList.add('hidden');
            audioVisualizer.classList.add('hidden');
            instruction.textContent = 'Tap to start recording';
        }
    }

    startRecordingTimer() {
        this.recordingTimer = setInterval(() => {
            const elapsed = Math.floor((Date.now() - this.recordingStartTime) / 1000);
            const minutes = Math.floor(elapsed / 60);
            const seconds = elapsed % 60;
            document.getElementById('recording-timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }

    async processRecording() {
        try {
            const audioBlob = new Blob(this.audioChunks, { type: 'audio/webm' });

            // Convert to base64 for API transmission
            const arrayBuffer = await audioBlob.arrayBuffer();
            const base64Audio = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

            await this.transcribeAndTranslate(base64Audio);

        } catch (error) {
            console.error('Processing error:', error);
            this.showError('Failed to process recording. Please try again.');
        }
    }

    async transcribeAndTranslate(audioData) {
        const sourceLang = document.getElementById('source-lang').value;
        const targetLang = document.getElementById('target-lang').value;
        const authToken = sessionStorage.getItem('authToken');

        this.updateStatus('Transcribing speech...');

        try {
            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    action: 'voice-translate',
                    audioData: audioData,
                    sourceLang: sourceLang,
                    targetLang: targetLang
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.transcription) {
                this.showTranscription(data.transcription);
                this.updateStatus('Translating...');

                if (data.translation) {
                    this.showTranslation(data.translation);
                    this.updateStatus('Ready for playback');
                }
            } else {
                throw new Error('No transcription received');
            }

        } catch (error) {
            console.error('Transcription error:', error);
            this.showError('Failed to transcribe audio. Please try again.');
        }
    }

    showTranscription(text) {
        document.getElementById('transcription-text').textContent = text;
        document.getElementById('transcription-section').classList.remove('hidden');
    }

    showTranslation(text) {
        document.getElementById('translation-text').textContent = text;
        document.getElementById('translation-section').classList.remove('hidden');
        document.getElementById('play-translation-btn').disabled = false;
    }

    async playTranslation() {
        const translationText = document.getElementById('translation-text').textContent;
        const targetLang = document.getElementById('target-lang').value;
        const playBtn = document.getElementById('play-translation-btn');
        const playIcon = document.getElementById('play-icon');
        const pauseIcon = document.getElementById('pause-icon');
        const playBtnText = document.getElementById('play-btn-text');

        if (!translationText) return;

        try {
            // Stop current audio if playing
            if (this.currentAudio && !this.currentAudio.paused) {
                this.currentAudio.pause();
                this.currentAudio = null;
                playIcon.classList.remove('hidden');
                pauseIcon.classList.add('hidden');
                playBtnText.textContent = 'Play Translation';
                return;
            }

            playBtn.disabled = true;
            playIcon.classList.add('hidden');
            pauseIcon.classList.remove('hidden');
            playBtnText.textContent = 'Loading...';

            const authToken = sessionStorage.getItem('authToken');

            const response = await fetch('/api/translate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                },
                body: JSON.stringify({
                    action: 'text-to-speech',
                    text: translationText,
                    language: targetLang
                })
            });

            if (!response.ok) {
                throw new Error('Failed to generate speech');
            }

            const audioBlob = await response.blob();
            const audioUrl = URL.createObjectURL(audioBlob);

            this.currentAudio = new Audio(audioUrl);

            this.currentAudio.onloadeddata = () => {
                playBtn.disabled = false;
                playBtnText.textContent = 'Playing...';
                this.currentAudio.play();
            };

            this.currentAudio.onended = () => {
                playIcon.classList.remove('hidden');
                pauseIcon.classList.add('hidden');
                playBtnText.textContent = 'Play Translation';
                playBtn.disabled = false;
                URL.revokeObjectURL(audioUrl);
            };

            this.currentAudio.onerror = () => {
                throw new Error('Audio playback failed');
            };

        } catch (error) {
            console.error('TTS error:', error);
            this.showError('Failed to play translation. Please try again.');
            playIcon.classList.remove('hidden');
            pauseIcon.classList.add('hidden');
            playBtnText.textContent = 'Play Translation';
            playBtn.disabled = false;
        }
    }

    showError(message) {
        this.updateStatus(`Error: ${message}`);
        // You could also show a toast notification here
    }

    updateStatus(message) {
        document.getElementById('status').textContent = message;
    }
}

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    new VoiceTranslator();
});
