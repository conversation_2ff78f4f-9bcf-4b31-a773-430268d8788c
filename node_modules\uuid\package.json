{"name": "uuid", "version": "3.3.2", "description": "RFC4122 (v1, v4, and v5) UUIDs", "commitlint": {"extends": ["@commitlint/config-conventional"]}, "keywords": ["uuid", "guid", "rfc4122"], "license": "MIT", "bin": {"uuid": "./bin/uuid"}, "devDependencies": {"@commitlint/cli": "7.0.0", "@commitlint/config-conventional": "7.0.1", "eslint": "4.19.1", "husky": "0.14.3", "mocha": "5.2.0", "runmd": "1.0.1", "standard-version": "4.4.0"}, "scripts": {"commitmsg": "commitlint -E GIT_PARAMS", "test": "mocha test/test.js", "md": "runmd --watch --output=README.md README_js.md", "release": "standard-version", "prepare": "runmd --output=README.md README_js.md"}, "browser": {"./lib/rng.js": "./lib/rng-browser.js", "./lib/sha1.js": "./lib/sha1-browser.js", "./lib/md5.js": "./lib/md5-browser.js"}, "repository": {"type": "git", "url": "https://github.com/kelektiv/node-uuid.git"}}