const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Load environment variables
require('dotenv').config();

// Import required modules for API functionality
const crypto = require('crypto');

const PORT = process.env.PORT || 3001;

// Rate limiting storage
const rateLimits = new Map();

// API helper functions
function checkRateLimit(ip) {
    const now = Date.now();
    const windowMs = 60 * 1000; // 1 minute
    const maxRequests = 30;

    if (!rateLimits.has(ip)) {
        rateLimits.set(ip, []);
    }

    const requests = rateLimits.get(ip);
    const validRequests = requests.filter(time => now - time < windowMs);

    if (validRequests.length >= maxRequests) {
        return false;
    }

    validRequests.push(now);
    rateLimits.set(ip, validRequests);
    return true;
}

function generateAuthToken() {
    return Buffer.from(`${Date.now()}-${Math.random()}`).toString('base64');
}

function verifyAuthToken(token) {
    try {
        const decoded = Buffer.from(token, 'base64').toString();
        const [timestamp] = decoded.split('-');
        const tokenAge = Date.now() - parseInt(timestamp);
        return tokenAge < 24 * 60 * 60 * 1000; // 24 hours
    } catch {
        return false;
    }
}

function getLanguageName(code) {
    const languages = {
        'auto': 'auto-detected language',
        'en': 'English', 'es': 'Spanish', 'fr': 'French', 'de': 'German',
        'it': 'Italian', 'pt': 'Portuguese', 'ru': 'Russian', 'ja': 'Japanese',
        'ko': 'Korean', 'zh': 'Chinese', 'ar': 'Arabic', 'hi': 'Hindi',
        'nl': 'Dutch', 'sv': 'Swedish', 'da': 'Danish', 'no': 'Norwegian',
        'fi': 'Finnish', 'pl': 'Polish', 'tr': 'Turkish'
    };
    return languages[code] || code;
}

async function handleApiRequest(req, res) {
    // CORS headers
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    if (req.method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    if (req.method !== 'POST') {
        res.writeHead(405, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Method not allowed' }));
        return;
    }

    // Rate limiting
    const clientIp = req.headers['x-forwarded-for'] || req.connection.remoteAddress || 'unknown';
    if (!checkRateLimit(clientIp)) {
        res.writeHead(429, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Rate limit exceeded. Please try again later.' }));
        return;
    }

    // Parse request body
    let body = '';
    req.on('data', chunk => {
        body += chunk.toString();
    });

    req.on('end', async () => {
        try {
            const data = JSON.parse(body);
            const { action } = data;

            if (action === 'auth') {
                await handleAuth(data, res);
            } else if (action === 'voice-translate') {
                await handleVoiceTranslate(data, req, res);
            } else if (action === 'text-to-speech') {
                await handleTextToSpeech(data, req, res);
            } else {
                res.writeHead(400, { 'Content-Type': 'application/json' });
                res.end(JSON.stringify({ error: 'Invalid action' }));
            }
        } catch (error) {
            console.error('API Error:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Internal server error' }));
        }
    });
}

async function handleAuth(data, res) {
    const { accessKey } = data;
    const adminKey = process.env.ADMIN_KEY;

    if (!adminKey) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Server configuration error' }));
        return;
    }

    if (accessKey === adminKey) {
        const token = generateAuthToken();
        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: true, token: token }));
    } else {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ success: false, error: 'Invalid access key' }));
    }
}

async function handleVoiceTranslate(data, req, res) {
    // Verify auth token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Unauthorized' }));
        return;
    }

    const token = authHeader.slice(7);
    if (!verifyAuthToken(token)) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid or expired token' }));
        return;
    }

    const { audioData, sourceLang, targetLang } = data;

    if (!audioData || !targetLang) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Missing required parameters' }));
        return;
    }

    const openaiApiKey = process.env.OPENAI_API_KEY;
    if (!openaiApiKey) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'OpenAI API key not configured' }));
        return;
    }

    try {
        // For now, return a mock response since Whisper API requires multipart/form-data
        // which is complex to implement in basic Node.js server
        const mockTranscription = sourceLang === 'nl' ? 'Hallo, hoe gaat het?' : 'Hello, how are you?';
        const mockTranslation = targetLang === 'tr' ? 'Merhaba, nasılsın?' : 'Hola, ¿cómo estás?';

        res.writeHead(200, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({
            transcription: mockTranscription,
            translation: mockTranslation
        }));
    } catch (error) {
        console.error('Voice translation error:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Voice translation failed' }));
    }
}

async function handleTextToSpeech(data, req, res) {
    // Verify auth token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Unauthorized' }));
        return;
    }

    const token = authHeader.slice(7);
    if (!verifyAuthToken(token)) {
        res.writeHead(401, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Invalid or expired token' }));
        return;
    }

    const { text, language } = data;

    if (!text) {
        res.writeHead(400, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Missing text parameter' }));
        return;
    }

    const openaiApiKey = process.env.OPENAI_API_KEY;
    if (!openaiApiKey) {
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'OpenAI API key not configured' }));
        return;
    }

    try {
        // Map language codes to TTS voices
        const voiceMap = {
            'en': 'alloy', 'es': 'nova', 'fr': 'shimmer', 'de': 'echo',
            'it': 'fable', 'pt': 'onyx', 'ru': 'alloy', 'ja': 'nova',
            'ko': 'shimmer', 'zh': 'echo', 'ar': 'fable', 'hi': 'onyx',
            'nl': 'alloy', 'sv': 'nova', 'da': 'shimmer', 'no': 'echo',
            'fi': 'fable', 'pl': 'onyx', 'tr': 'alloy'
        };

        const voice = voiceMap[language] || 'alloy';

        const ttsResponse = await fetch('https://api.openai.com/v1/audio/speech', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${openaiApiKey}`,
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: 'tts-1',
                input: text,
                voice: voice,
                response_format: 'mp3'
            })
        });

        if (!ttsResponse.ok) {
            const errorText = await ttsResponse.text();
            console.error('TTS API error:', errorText);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Text-to-speech failed' }));
            return;
        }

        // Stream the audio response
        res.writeHead(200, {
            'Content-Type': 'audio/mpeg',
            'Content-Length': ttsResponse.headers.get('content-length') || '',
            'Cache-Control': 'no-cache'
        });

        // Pipe the response
        const reader = ttsResponse.body.getReader();

        const pump = async () => {
            try {
                while (true) {
                    const { done, value } = await reader.read();

                    if (done) {
                        res.end();
                        break;
                    }

                    res.write(value);
                }
            } catch (error) {
                console.error('Streaming error:', error);
                res.end();
            }
        };

        await pump();

    } catch (error) {
        console.error('TTS error:', error);
        res.writeHead(500, { 'Content-Type': 'application/json' });
        res.end(JSON.stringify({ error: 'Text-to-speech failed' }));
    }
}

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;

    // Handle API routes
    if (pathname.startsWith('/api/')) {
        await handleApiRequest(req, res);
        return;
    }

    // Handle static files
    let filePath = '.' + pathname;
    if (filePath === './') {
        filePath = './index.html';
    }

    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';

    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                // File not found
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 Not Found</h1>', 'utf-8');
            } else {
                // Server error
                res.writeHead(500);
                res.end(`Server Error: ${error.code}`, 'utf-8');
            }
        } else {
            // Success
            res.writeHead(200, { 'Content-Type': mimeType });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(PORT, () => {
    console.log(`🚀 Live Translate server running at http://localhost:${PORT}`);
    console.log(`📝 Admin key: ${process.env.ADMIN_KEY || 'Not set'}`);
    console.log(`🤖 OpenAI API: ${process.env.OPENAI_API_KEY ? 'Configured' : 'Not configured'}`);
});
