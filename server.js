const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

// Import the API handler
const translateHandler = require('./api/translate.js');

// Load environment variables
require('dotenv').config();

const PORT = process.env.PORT || 3000;

// MIME types
const mimeTypes = {
    '.html': 'text/html',
    '.js': 'text/javascript',
    '.css': 'text/css',
    '.json': 'application/json',
    '.png': 'image/png',
    '.jpg': 'image/jpg',
    '.gif': 'image/gif',
    '.svg': 'image/svg+xml',
    '.wav': 'audio/wav',
    '.mp4': 'video/mp4',
    '.woff': 'application/font-woff',
    '.ttf': 'application/font-ttf',
    '.eot': 'application/vnd.ms-fontobject',
    '.otf': 'application/font-otf',
    '.wasm': 'application/wasm'
};

const server = http.createServer(async (req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;

    // Handle API routes
    if (pathname.startsWith('/api/')) {
        try {
            // Create a mock Vercel-like request/response object
            const mockReq = {
                method: req.method,
                headers: req.headers,
                body: null,
                connection: { remoteAddress: req.connection.remoteAddress }
            };

            const mockRes = {
                statusCode: 200,
                headers: {},
                setHeader: (key, value) => {
                    mockRes.headers[key] = value;
                },
                status: (code) => {
                    mockRes.statusCode = code;
                    return mockRes;
                },
                json: (data) => {
                    res.writeHead(mockRes.statusCode, {
                        'Content-Type': 'application/json',
                        ...mockRes.headers
                    });
                    res.end(JSON.stringify(data));
                },
                write: (chunk) => {
                    res.write(chunk);
                },
                end: () => {
                    res.end();
                }
            };

            // Parse request body for POST requests
            if (req.method === 'POST') {
                let body = '';
                req.on('data', chunk => {
                    body += chunk.toString();
                });
                req.on('end', async () => {
                    try {
                        mockReq.body = JSON.parse(body);
                        await translateHandler.default(mockReq, mockRes);
                    } catch (error) {
                        console.error('API Error:', error);
                        res.writeHead(500, { 'Content-Type': 'application/json' });
                        res.end(JSON.stringify({ error: 'Internal server error' }));
                    }
                });
            } else {
                await translateHandler.default(mockReq, mockRes);
            }
        } catch (error) {
            console.error('API Error:', error);
            res.writeHead(500, { 'Content-Type': 'application/json' });
            res.end(JSON.stringify({ error: 'Internal server error' }));
        }
        return;
    }

    // Handle static files
    let filePath = '.' + pathname;
    if (filePath === './') {
        filePath = './index.html';
    }

    const extname = String(path.extname(filePath)).toLowerCase();
    const mimeType = mimeTypes[extname] || 'application/octet-stream';

    fs.readFile(filePath, (error, content) => {
        if (error) {
            if (error.code === 'ENOENT') {
                // File not found
                res.writeHead(404, { 'Content-Type': 'text/html' });
                res.end('<h1>404 Not Found</h1>', 'utf-8');
            } else {
                // Server error
                res.writeHead(500);
                res.end(`Server Error: ${error.code}`, 'utf-8');
            }
        } else {
            // Success
            res.writeHead(200, { 'Content-Type': mimeType });
            res.end(content, 'utf-8');
        }
    });
});

server.listen(PORT, () => {
    console.log(`🚀 Live Translate server running at http://localhost:${PORT}`);
    console.log(`📝 Admin key: ${process.env.ADMIN_KEY || 'Not set'}`);
    console.log(`🤖 OpenAI API: ${process.env.OPENAI_API_KEY ? 'Configured' : 'Not configured'}`);
});
