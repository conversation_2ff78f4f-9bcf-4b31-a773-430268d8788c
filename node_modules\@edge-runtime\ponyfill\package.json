{"name": "@edge-runtime/ponyfill", "description": "A ponyfill (doesn't overwrite the native methods) to use Edge Runtime APIs in any environment.", "homepage": "https://edge-runtime.vercel.app/packages/ponyfill", "version": "2.4.1", "main": "src/index.js", "module": "dist/index.mjs", "repository": {"directory": "packages/ponyfill", "type": "git", "url": "git+https://github.com/vercel/edge-runtime.git"}, "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "keywords": ["apis", "edge", "edge-runtime", "functions", "polyfill", "ponyfill", "primitives", "runtime", "shim", "standard", "web"], "devDependencies": {"acorn": "8.10.0", "acorn-loose": "8.3.0", "acorn-walk": "8.2.0", "@edge-runtime/jest-environment": "2.3.4", "@edge-runtime/primitives": "4.0.2", "@edge-runtime/vm": "3.1.4"}, "engines": {"node": ">=16"}, "files": ["src"], "license": "MPL-2.0", "publishConfig": {"access": "public"}, "types": "src/index.d.ts", "scripts": {"clean": "rm -rf node_modules", "test": "pnpm test:edge && pnpm test:node", "test:edge": "EDGE_RUNTIME_EXISTS=true jest --env=@edge-runtime/jest-environment --testPathIgnorePatterns='.node.test.ts$'", "test:node": "jest --env=node"}}