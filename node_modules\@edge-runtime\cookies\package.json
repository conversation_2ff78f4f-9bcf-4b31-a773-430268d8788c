{"name": "@edge-runtime/cookies", "description": "Cookie helpers compatible with Edge Runtime", "homepage": "https://edge-runtime.vercel.app/packages/cookies", "version": "3.4.1", "main": "dist/index.js", "module": "dist/index.mjs", "repository": {"directory": "packages/cookies", "type": "git", "url": "git+https://github.com/vercel/edge-runtime.git"}, "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "keywords": ["cookie", "cookies", "edge", "edge-runtime", "functions", "runtime", "set-cookie", "standard", "web"], "devDependencies": {"@types/cookie": "0.5.2", "tsup": "7", "@edge-runtime/format": "2.2.0", "@edge-runtime/jest-environment": "2.3.1"}, "engines": {"node": ">=16"}, "files": ["dist"], "license": "MPL-2.0", "publishConfig": {"access": "public"}, "types": "dist/index.d.ts", "scripts": {"build": "tsup", "clean:build": "rm -rf dist", "clean:node": "rm -rf node_modules", "prebuild": "pnpm run clean:build", "test": "jest"}}