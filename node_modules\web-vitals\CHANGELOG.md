# Changelog

### v0.2.4 (2020-07-23)

- Remove the unload listener ([#68](https://github.com/GoogleChrome/web-vitals/pull/68))

### v0.2.3 (2020-06-26)

- Ensure reports only occur if a PO was created ([#58](https://github.com/GoogleChrome/web-vitals/pull/58))

### v0.2.2 (2020-05-12)

- Remove package `type` field ([#35](https://github.com/GoogleChrome/web-vitals/pull/35))

### v0.2.1 (2020-05-06)

- Ensure all modules are pure modules ([#23](https://github.com/GoogleChrome/web-vitals/pull/23))
- Ensure proper TypeScript exports and config ([#22](https://github.com/GoogleChrome/web-vitals/pull/22))

### v0.2.0 (2020-05-03)

- Initial public release

### v0.1.0 (2020-04-24)

- Initial pre-release
