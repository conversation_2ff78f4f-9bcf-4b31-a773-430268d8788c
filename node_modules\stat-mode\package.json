{"name": "stat-mode", "version": "0.3.0", "description": "Offers convenient getters and setters for the stat `mode`", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/TooTallNate/stat-mode.git"}, "keywords": ["stat", "mode", "owner", "group", "others", "chmod", "octal", "symbolic", "permissions"], "author": "<PERSON> <<EMAIL>> (http://n8.io/)", "license": "MIT", "bugs": {"url": "https://github.com/TooTallNate/stat-mode/issues"}, "homepage": "https://github.com/TooTallNate/stat-mode", "devDependencies": {"mocha": "^3.0.2"}, "scripts": {"test": "mocha --reporter spec"}}